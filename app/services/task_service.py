"""
Task service for handling adaptation task operations
"""
import os
import logging
import tempfile
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, List
from flask import current_app, flash
from flask_login import current_user
from werkzeug.utils import secure_filename
from werkzeug.datastructures import FileStorage
from app import db
from app.models import AdaptationTask
from app.utils.file_utils import save_uploaded_file
from app.tasks import process_adaptation_task

logger = logging.getLogger(__name__)


class TaskService:
    """Service for handling adaptation task operations"""
    
    @staticmethod
    def get_user_tasks() -> List[AdaptationTask]:
        """Get all tasks for current user"""
        return AdaptationTask.query.filter_by(
            user_id=current_user.id
        ).order_by(AdaptationTask.created_at.desc()).all()
    
    @staticmethod
    def get_user_task(task_id: int) -> AdaptationTask:
        """Get specific task for current user"""
        return AdaptationTask.query.filter_by(
            id=task_id, 
            user_id=current_user.id
        ).first_or_404()
    
    @staticmethod
    def create_task_from_upload(file: FileStorage, form_data: Dict[str, Any]) -> Optional[AdaptationTask]:
        """Create adaptation task from uploaded file"""
        try:
            # Save uploaded file
            file_path = save_uploaded_file(file, current_user.id)
            if not file_path:
                flash('文件上传失败！', 'error')
                return None
            
            # Create task
            task_name = f"{form_data['book_name']} - {secure_filename(file.filename)}"
            task = AdaptationTask(
                user_id=current_user.id,
                task_name=task_name,
                original_filename=secure_filename(file.filename),
                file_path=file_path,
                character=form_data['character'],
                book_name=form_data['book_name'],
                channel=form_data['channel'],
                person=form_data['person']
            )
            
            db.session.add(task)
            db.session.commit()
            
            # Start async task
            celery_task = process_adaptation_task.delay(task.id)
            task.celery_task_id = celery_task.id
            db.session.commit()
            
            flash('文件上传成功，开始故事优化处理！', 'success')
            return task
            
        except Exception as e:
            logger.error(f"创建上传任务失败: {str(e)}")
            flash('创建任务失败！', 'error')
            return None
    
    @staticmethod
    def create_task_from_content(content: str, form_data: Dict[str, Any]) -> Optional[AdaptationTask]:
        """Create adaptation task from text content"""
        try:
            # Create user directory
            user_dir = Path(current_app.config['UPLOAD_FOLDER']) / str(current_user.id)
            user_dir.mkdir(parents=True, exist_ok=True)
            
            # Save content to temporary file
            temp_filename = f"quick_{uuid.uuid4().hex}.txt"
            temp_path = user_dir / temp_filename
            
            with open(temp_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # Create task
            task_name = f"{form_data['book_name']} - 快速优化"
            task = AdaptationTask(
                user_id=current_user.id,
                task_name=task_name,
                original_filename="快速优化内容.txt",
                file_path=str(temp_path),
                character=form_data['character'],
                book_name=form_data['book_name'],
                channel=form_data['channel'],
                person=form_data['person']
            )
            
            db.session.add(task)
            db.session.commit()
            
            # Start async task
            celery_task = process_adaptation_task.delay(task.id)
            task.celery_task_id = celery_task.id
            db.session.commit()
            
            flash('内容提交成功，开始故事优化处理！', 'success')
            return task
            
        except Exception as e:
            logger.error(f"创建快速优化任务失败: {str(e)}")
            flash('创建任务失败！', 'error')
            return None
    
    @staticmethod
    def get_task_status(task_id: int) -> Dict[str, Any]:
        """Get comprehensive task status including Celery status"""
        try:
            task = TaskService.get_user_task(task_id)
            
            result = {
                'status': task.status,
                'progress': task.progress or 0,
                'total_chapters': task.total_chapters or 0,
                'processed_chapters': task.processed_chapters or 0,
                'created_at': task.created_at.isoformat() if task.created_at else None,
                'started_at': task.started_at.isoformat() if task.started_at else None,
                'completed_at': task.completed_at.isoformat() if task.completed_at else None,
                'error_message': task.error_message
            }
            
            # Get Celery task status if available
            if task.celery_task_id:
                celery_status = TaskService._get_celery_status(task)
                result.update(celery_status)
            
            return result
            
        except Exception as e:
            logger.error(f"获取任务状态失败: {str(e)}")
            return {'error': '获取任务状态失败', 'message': str(e)}
    
    @staticmethod
    def _get_celery_status(task: AdaptationTask) -> Dict[str, Any]:
        """Get Celery task status"""
        try:
            from app import celery
            from celery.result import AsyncResult
            
            celery_result = AsyncResult(task.celery_task_id, app=celery)
            result = {}
            
            if celery_result.state == 'PROGRESS':
                if celery_result.info and isinstance(celery_result.info, dict):
                    for key in ['current', 'total', 'status', 'chapters_current', 'chapters_total']:
                        if key in celery_result.info:
                            result[key] = celery_result.info[key]
            elif celery_result.state == 'FAILURE':
                error_info = str(celery_result.info) if celery_result.info else "未知错误"
                result['error_message'] = error_info
            elif celery_result.state == 'SUCCESS':
                if task.status != 'completed':
                    task.status = 'completed'
                    task.progress = 100
                    db.session.commit()
                    result['status'] = 'completed'
                    result['progress'] = 100
            elif celery_result.state == 'PENDING':
                result['status'] = 'pending'
            
            return result
            
        except Exception as e:
            logger.error(f"获取Celery状态失败: {str(e)}")
            return {'celery_error': str(e)}
    
    @staticmethod
    def delete_task(task_id: int) -> bool:
        """Delete task and associated files"""
        try:
            task = TaskService.get_user_task(task_id)
            
            # Cancel Celery task if running
            if task.celery_task_id and task.status == 'processing':
                from celery.result import AsyncResult
                celery_result = AsyncResult(task.celery_task_id)
                celery_result.revoke(terminate=True)
            
            # Delete associated files
            TaskService._delete_task_files(task)
            
            # Delete database record
            db.session.delete(task)
            db.session.commit()
            
            flash('任务已删除！', 'success')
            return True
            
        except Exception as e:
            logger.error(f"删除任务失败: {str(e)}")
            flash('删除任务失败！', 'error')
            return False
    
    @staticmethod
    def _delete_task_files(task: AdaptationTask):
        """Delete files associated with task"""
        try:
            if task.file_path and os.path.exists(task.file_path):
                os.remove(task.file_path)
            if task.output_path and os.path.exists(task.output_path):
                os.remove(task.output_path)
        except Exception as e:
            logger.error(f"删除文件失败: {str(e)}")
    
    @staticmethod
    def is_task_downloadable(task: AdaptationTask) -> bool:
        """Check if task result is downloadable"""
        return (task.is_completed and 
                task.output_path and 
                os.path.exists(task.output_path))
    
    @staticmethod
    def get_debug_info(task_id: int) -> Dict[str, Any]:
        """Get debug information for task"""
        try:
            task = TaskService.get_user_task(task_id)
            
            debug_info = {
                'task_id': task.id,
                'task_name': task.task_name,
                'status': task.status,
                'progress': task.progress,
                'celery_task_id': task.celery_task_id,
                'error_message': task.error_message,
                'created_at': task.created_at.isoformat() if task.created_at else None,
            }
            
            if task.celery_task_id:
                try:
                    from app import celery
                    from celery.result import AsyncResult
                    celery_result = AsyncResult(task.celery_task_id, app=celery)
                    
                    debug_info.update({
                        'celery_state': celery_result.state,
                        'celery_info': str(celery_result.info),
                        'celery_ready': celery_result.ready(),
                        'celery_successful': celery_result.successful() if celery_result.ready() else None
                    })
                except Exception as e:
                    debug_info['celery_error'] = str(e)
            
            return debug_info
            
        except Exception as e:
            return {'error': str(e)}
