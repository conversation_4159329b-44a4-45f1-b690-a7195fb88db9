"""
Text rewriting service for story adaptation
"""
import logging
from pathlib import Path
from typing import Dict, Optional
from jinja2 import Environment, FileSystemLoader
from app.api_client import get_api_client
from app.utils.text_utils import BlockError, retry_operation
from app.utils.similarity_utils import find_similar_chunks

logger = logging.getLogger(__name__)


class TextRewriter:
    """Text rewriting service for story adaptation"""
    
    def __init__(self, character: str, book_name: str, channel: str, person: str, key_func):
        """Initialize the TextRewriter with model configuration."""
        self.character = character
        self.book_name = book_name
        self.channel = channel
        self.person = person
        self.key_func = key_func

        # 获取API客户端
        self.api_client = get_api_client()

        # 初始化Jinja2模板环境
        template_dir = Path(__file__).parent.parent / 'prompt_templates'
        self.jinja_env = Environment(loader=FileSystemLoader(str(template_dir)))

    def get_system_prompt(self) -> str:
        """获取系统提示词"""
        template = self.jinja_env.get_template('system_prompt.j2')
        return template.render(
            person=self.person,
            channel=self.channel
        )

    def initial_rewrite(self, text: str, pre: str) -> Dict[str, str]:
        """First rewrite of the input text using the Gemini model."""
        template = self.jinja_env.get_template('initial_prompt.j2')
        initial_prompt = template.render(
            pre_text=pre,
            character=self.character,
            text_length=len(text),
            min_output_length=int(len(text) * 0.7),
            max_output_length=int(len(text) * 0.8),
            text=text
        )

        logger.info(f"-------------------Initial rewriting-------------------")
        try:
            logger.info(f"Initial rewriting context length: {len(text)}")
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": initial_prompt}
            ]
            response = self.api_client.send_chat_completion(messages)
            if response:
                logger.info(f"Initial rewriting result length: {len(response)}")
                logger.debug(f"Initial rewriting result preview: {response[:200]}...")
            else:
                logger.warning("Initial rewriting returned empty response")
            return {
                "rewritten_text": response,
                "original_prompt": initial_prompt
            } if response else {"rewritten_text": None, "original_prompt": initial_prompt}
        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during initial rewriting: {str(e)}")
            return {"rewritten_text": None, "original_prompt": initial_prompt}

    def optimize_rewrite(self, original_text: str, first_rewrite: str) -> Dict[str, str]:
        """Optimize the first rewrite using the Gemini model."""
        template = self.jinja_env.get_template('optimize_prompt.j2')
        review_prompt = template.render(
            original_text=original_text,
            first_rewrite=first_rewrite
        )

        logger.info(f"-------------------Optimization rewriting-------------------")
        try:
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": review_prompt}
            ]
            response = self.api_client.send_chat_completion(messages)
            logger.info(f"Optimization rewriting result length: {len(response) if response else 0}")
            return {
                "rewritten_text": response,
                "original_prompt": review_prompt
            }
        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during optimization rewriting: {str(e)}")
            return {"rewritten_text": None, "original_prompt": review_prompt}

    def further_rewrite(self, original_text: str, origin_prompt: str, rewritten_text: str) -> Dict[str, str]:
        """Perform additional rewriting based on similar text chunks."""
        similar_chunks = find_similar_chunks(rewritten_text, original_text)
        logger.info(f"-------------------Further rewriting-------------------")
        if not similar_chunks:
            logger.info("No text chunks requiring further rewriting found.")
            return {"rewritten_text": rewritten_text}

        # 准备相似文本块数据
        chunk_data = []
        for rewrite, _, sim in similar_chunks:
            clean_text = rewrite.replace('\n', ' ')
            chunk_data.append({
                'text': clean_text,
                'similarity': sim
            })

        template = self.jinja_env.get_template('further_rewrite_prompt.j2')
        prompt = template.render(similar_chunks=chunk_data)

        try:
            messages = [
                {"role": "system", "content": self.get_system_prompt()},
                {"role": "user", "content": origin_prompt},
                {"role": "assistant", "content": rewritten_text},
                {"role": "user", "content": prompt}
            ]
            response = self.api_client.send_chat_completion(messages)
            logger.info(f"Further rewriting result length: {len(response) if response else 0}")
            return {"rewritten_text": response}

        except BlockError as e:
            raise e
        except Exception as e:
            logger.error(f"Error during further rewriting: {str(e)}")
            return {"rewritten_text": None}

    def process_chapter_text(self, chapter_text: str, pre_text: str, num_attempts: int, chapter_name: str) -> Optional[str]:
        """处理章节文本并返回最佳结果"""
        results = []
        max_attempts = num_attempts * 2
        attempts = 0

        while len(results) < num_attempts and attempts < max_attempts:
            attempts += 1
            logger.info(f"Attempt {attempts} for {chapter_name}")

            # 尝试重写
            result = self._attempt_rewrite_chapter(chapter_text, pre_text)
            if result:
                results.append(result)
                logger.info(f"Successful attempt {attempts} for {chapter_name} finished with {len(result)} characters.")

        if not results:
            logger.error(f"Failed to rewrite {chapter_name} after {max_attempts} attempts")
            # 降级策略：返回原文本而不是None
            logger.warning(f"使用降级策略：返回原始文本作为{chapter_name}的结果")
            return chapter_text

        # 返回最长的结果
        best_result = max(results, key=lambda x: len(x))
        logger.info(f"成功处理{chapter_name}，选择了长度为{len(best_result)}的最佳结果")
        return best_result

    def _attempt_rewrite_chapter(self, chapter_text: str, pre_text: str,
                               enable_optimization: bool = True, max_retries: int = 10, delay: float = 1.0) -> Optional[str]:
        """尝试一次章节文本的重写"""
        # 尝试initial_rewrite
        initial_result = retry_operation(
            self.initial_rewrite,
            max_retries,
            delay,
            chapter_text,
            pre_text
        )
        if not initial_result:
            logger.error(f"Initial rewriting failed after {max_retries} retries for chapter text")
            return None

        if enable_optimization:
            # 尝试optimize_rewrite
            optimized_result = retry_operation(
                self.optimize_rewrite,
                max_retries,
                delay,
                chapter_text,
                initial_result["rewritten_text"]
            )
            if not optimized_result:
                logger.error(f"Optimization failed after {max_retries} retries for chapter text")
                optimized_result = {"rewritten_text": initial_result["rewritten_text"]}
        else:
            optimized_result = {"rewritten_text": initial_result["rewritten_text"]}

        # 尝试further_rewrite
        further_result = retry_operation(
            self.further_rewrite,
            max_retries,
            delay,
            chapter_text,
            initial_result['original_prompt'],
            optimized_result["rewritten_text"]
        )
        if not further_result:
            logger.error(f"Further rewriting failed after {max_retries} retries for chapter text")
            further_result = {"rewritten_text": initial_result["rewritten_text"]}

        return further_result["rewritten_text"]
